import { render, screen, userEvent, a11yTestUtils } from '@/test-utils'
import { 
  AccessibleMedicalData, 
  AccessiblePatientCard, 
  AccessibleMedicalAlert 
} from '@/components/ui/accessible-components'
import { mockPatientData } from '@/test-utils'

describe('AccessibleMedicalData', () => {
  const defaultProps = {
    patientName: '<PERSON><PERSON> Sharma',
    dataType: 'Blood Pressure',
    value: '120/80',
    unit: 'mmHg',
  }

  it('renders medical data with proper accessibility attributes', () => {
    render(<AccessibleMedicalData {...defaultProps} />)
    
    const container = screen.getByRole('region')
    expect(container).toHaveAttribute('aria-label', 'Priya Sharma Blood Pressure: 120/80 mmHg')
    expect(container).toHaveAttribute('aria-live', 'polite')
  })

  it('displays critical data with assertive aria-live', () => {
    render(
      <AccessibleMedicalData 
        {...defaultProps} 
        severity="critical"
        value="180/120"
      />
    )
    
    const container = screen.getByRole('region')
    expect(container).toHaveAttribute('aria-live', 'assertive')
    expect(container).toHaveClass('border-red-500/50')
  })

  it('shows warning indicator for warning severity', () => {
    render(
      <AccessibleMedicalData 
        {...defaultProps} 
        severity="warning"
        value="140/90"
      />
    )
    
    const container = screen.getByRole('region')
    expect(container).toHaveClass('border-yellow-500/50')
    expect(screen.getByText('140/90')).toHaveClass('text-yellow-400')
  })

  it('displays critical alert icon for critical severity', () => {
    render(
      <AccessibleMedicalData 
        {...defaultProps} 
        severity="critical"
        value="200/130"
      />
    )
    
    expect(screen.getByRole('region')).toContainHTML('svg')
  })
})

describe('AccessiblePatientCard', () => {
  const user = userEvent.setup()

  it('renders patient information with proper accessibility', () => {
    const mockOnClick = jest.fn()
    render(
      <AccessiblePatientCard 
        patient={mockPatientData} 
        onClick={mockOnClick}
      />
    )
    
    const card = screen.getByRole('button')
    expect(card).toHaveAttribute('aria-label', 
      'Patient Priya Sharma, age 42, Glioblastoma stage IV, status active, treatment 65% complete'
    )
    expect(card).toHaveAttribute('tabindex', '0')
  })

  it('handles click events', async () => {
    const mockOnClick = jest.fn()
    render(
      <AccessiblePatientCard 
        patient={mockPatientData} 
        onClick={mockOnClick}
      />
    )
    
    const card = screen.getByRole('button')
    await user.click(card)
    
    expect(mockOnClick).toHaveBeenCalledTimes(1)
  })

  it('handles keyboard navigation', async () => {
    const mockOnClick = jest.fn()
    render(
      <AccessiblePatientCard 
        patient={mockPatientData} 
        onClick={mockOnClick}
      />
    )
    
    const card = screen.getByRole('button')
    card.focus()
    
    // Test Enter key
    await user.keyboard('{Enter}')
    expect(mockOnClick).toHaveBeenCalledTimes(1)
    
    // Test Space key
    await user.keyboard(' ')
    expect(mockOnClick).toHaveBeenCalledTimes(2)
  })

  it('displays patient status with proper color coding', () => {
    render(<AccessiblePatientCard patient={mockPatientData} />)
    
    const statusBadge = screen.getByLabelText('Status: active')
    expect(statusBadge).toHaveClass('text-blue-400')
  })

  it('shows treatment progress with accessible format', () => {
    render(<AccessiblePatientCard patient={mockPatientData} />)
    
    expect(screen.getByText('65%')).toBeInTheDocument()
    
    // Check for progress bar
    const progressBar = screen.getByRole('button').querySelector('[style*="width: 65%"]')
    expect(progressBar).toBeInTheDocument()
  })

  it('optimizes touch targets for tablet users', () => {
    // Mock tablet optimization
    jest.mock('@/hooks/use-mobile', () => ({
      useMedicalTabletOptimization: () => ({
        shouldUseLargerTouchTargets: true,
      }),
    }))
    
    render(<AccessiblePatientCard patient={mockPatientData} />)
    
    const card = screen.getByRole('button')
    expect(card).toHaveClass('min-h-[120px]')
  })
})

describe('AccessibleMedicalAlert', () => {
  const user = userEvent.setup()

  it('renders info alert with proper accessibility', () => {
    render(
      <AccessibleMedicalAlert
        severity="info"
        title="Information"
        message="This is an informational message"
      />
    )
    
    const alert = screen.getByRole('alert')
    expect(alert).toHaveAttribute('aria-live', 'polite')
    expect(alert).toHaveAttribute('aria-labelledby')
    expect(alert).toHaveAttribute('aria-describedby')
  })

  it('renders error alert with assertive announcement', () => {
    render(
      <AccessibleMedicalAlert
        severity="error"
        title="Critical Error"
        message="Patient vitals are critical"
      />
    )
    
    const alert = screen.getByRole('alert')
    expect(alert).toHaveAttribute('aria-live', 'assertive')
    expect(alert).toHaveClass('bg-red-500/10', 'border-red-500/30')
  })

  it('handles dismiss functionality', async () => {
    const mockOnDismiss = jest.fn()
    render(
      <AccessibleMedicalAlert
        severity="warning"
        title="Warning"
        message="Check patient medication"
        onDismiss={mockOnDismiss}
      />
    )
    
    const dismissButton = screen.getByLabelText('Dismiss alert')
    await user.click(dismissButton)
    
    expect(mockOnDismiss).toHaveBeenCalledTimes(1)
  })

  it('displays appropriate icons for different severities', () => {
    const { rerender } = render(
      <AccessibleMedicalAlert
        severity="success"
        title="Success"
        message="Treatment completed successfully"
      />
    )
    
    expect(screen.getByRole('alert')).toContainHTML('svg')
    
    rerender(
      <AccessibleMedicalAlert
        severity="warning"
        title="Warning"
        message="Review required"
      />
    )
    
    expect(screen.getByRole('alert')).toContainHTML('svg')
  })

  it('uses proper color schemes for medical context', () => {
    render(
      <AccessibleMedicalAlert
        severity="error"
        title="Critical"
        message="Immediate attention required"
      />
    )
    
    const alert = screen.getByRole('alert')
    expect(alert).toHaveClass('bg-red-500/10', 'border-red-500/30')
    
    const title = screen.getByText('Critical')
    expect(title).toHaveClass('text-red-400')
  })

  it('meets accessibility standards', () => {
    render(
      <AccessibleMedicalAlert
        severity="info"
        title="Patient Update"
        message="Lab results are available"
      />
    )
    
    const alert = screen.getByRole('alert')
    
    // Check ARIA attributes
    a11yTestUtils.expectAriaLabel(alert, expect.any(String))
    
    // Check for proper role
    expect(alert).toHaveAttribute('role', 'alert')
    
    // Check for live region
    expect(alert).toHaveAttribute('aria-live')
  })
})

describe('Accessibility Integration', () => {
  it('maintains proper heading hierarchy', () => {
    render(
      <div>
        <h1>Patient Dashboard</h1>
        <AccessiblePatientCard patient={mockPatientData} />
        <AccessibleMedicalData
          patientName="Priya Sharma"
          dataType="Temperature"
          value="37.2"
          unit="°C"
        />
      </div>
    )
    
    const container = screen.getByRole('main') || document.body
    a11yTestUtils.expectHeadingHierarchy(container)
  })

  it('supports keyboard navigation between components', async () => {
    const user = userEvent.setup()
    
    render(
      <div>
        <AccessiblePatientCard patient={mockPatientData} onClick={jest.fn()} />
        <AccessibleMedicalAlert
          severity="info"
          title="Info"
          message="Test message"
          onDismiss={jest.fn()}
        />
      </div>
    )
    
    // Tab through interactive elements
    await user.tab()
    expect(screen.getByRole('button', { name: /Patient Priya Sharma/ })).toHaveFocus()
    
    await user.tab()
    expect(screen.getByLabelText('Dismiss alert')).toHaveFocus()
  })

  it('provides proper screen reader announcements', () => {
    render(
      <AccessibleMedicalData
        patientName="Priya Sharma"
        dataType="Heart Rate"
        value="72"
        unit="bpm"
        severity="normal"
      />
    )
    
    const region = screen.getByRole('region')
    expect(region).toHaveAttribute('aria-label', 'Priya Sharma Heart Rate: 72 bpm')
  })
})
