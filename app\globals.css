@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 271 91% 65%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 271 91% 65%;

    --radius: 0.5rem;
  }

  .dark {
    /* High-contrast dark theme with deep black backgrounds */
    --background: 0 0% 0%; /* Pure black background */
    --foreground: 0 0% 100%; /* Pure white text */

    --card: 0 0% 2%; /* Very dark card background */
    --card-foreground: 0 0% 100%; /* Pure white card text */

    --popover: 0 0% 2%;
    --popover-foreground: 0 0% 100%;

    --primary: 217 91% 60%; /* Bright blue primary */
    --primary-foreground: 0 0% 100%;

    --secondary: 0 0% 8%; /* Dark secondary */
    --secondary-foreground: 0 0% 100%;

    --muted: 0 0% 8%;
    --muted-foreground: 0 0% 70%; /* Light gray for muted text */

    --accent: 0 0% 8%;
    --accent-foreground: 0 0% 100%;

    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;

    --border: 217 50% 15%; /* Subtle blue border */
    --input: 0 0% 8%;
    --ring: 217 91% 60%; /* Bright blue focus ring */

    /* Custom variables for glowing effects */
    --glow-primary: 217 91% 60%;
    --glow-secondary: 200 100% 50%;
    --glow-accent: 180 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom styles for Quant-NEX - Enhanced High-Contrast Theme */
.glow-text {
  text-shadow:
    0 0 5px rgba(59, 130, 246, 0.8),
    0 0 10px rgba(59, 130, 246, 0.6),
    0 0 20px rgba(59, 130, 246, 0.4);
}

.glow-border {
  box-shadow:
    0 0 10px rgba(59, 130, 246, 0.6),
    0 0 20px rgba(59, 130, 246, 0.4),
    0 0 30px rgba(59, 130, 246, 0.2),
    inset 0 0 10px rgba(59, 130, 246, 0.1);
}

.glow-border-subtle {
  box-shadow:
    0 0 5px rgba(59, 130, 246, 0.3),
    0 0 10px rgba(59, 130, 246, 0.2),
    inset 0 0 5px rgba(59, 130, 246, 0.05);
}

.neon-glow {
  filter: drop-shadow(0 0 8px rgba(59, 130, 246, 0.8));
}

.card-glow {
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.2);
  box-shadow:
    0 0 20px rgba(59, 130, 246, 0.1),
    inset 0 0 20px rgba(59, 130, 246, 0.05);
}

.button-glow {
  box-shadow:
    0 0 10px rgba(59, 130, 246, 0.4),
    0 0 20px rgba(59, 130, 246, 0.2);
  transition: all 0.3s ease;
}

.button-glow:hover {
  box-shadow:
    0 0 15px rgba(59, 130, 246, 0.6),
    0 0 30px rgba(59, 130, 246, 0.4),
    0 0 45px rgba(59, 130, 246, 0.2);
  transform: translateY(-1px);
}

/* Gradient animations */
@keyframes gradient-shift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 5s ease infinite;
}

/* Responsive utilities */
.mobile-padding {
  @apply px-4 sm:px-6;
}

.mobile-text {
  @apply text-sm sm:text-base;
}

.mobile-heading {
  @apply text-xl sm:text-2xl lg:text-3xl;
}

.mobile-grid {
  @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4;
}

.mobile-flex {
  @apply flex flex-col sm:flex-row sm:items-center;
}

.mobile-button {
  @apply w-full sm:w-auto;
}

/* Improved mobile touch targets */
@media (max-width: 768px) {
  button, .button {
    min-height: 44px;
    min-width: 44px;
  }

  input, select, textarea {
    min-height: 44px;
  }
}
