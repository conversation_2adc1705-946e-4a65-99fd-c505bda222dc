// Medical Workflow E2E Tests for Quant-NEX

describe('Medical Professional Workflows', () => {
  beforeEach(() => {
    // Mock authentication
    cy.mockAuth('doctor')
    cy.visit('/')
  })

  describe('Patient Registration Workflow', () => {
    it('should complete patient registration successfully', () => {
      // Navigate to patients page
      cy.get('[data-testid="nav-patients"]').click()
      cy.url().should('include', '/patients')

      // Open new patient form
      cy.get('[data-testid="add-patient-button"]').click()
      cy.get('[data-testid="patient-form-modal"]').should('be.visible')

      // Fill patient information
      cy.get('[data-testid="patient-first-name"]').type('<PERSON>esh')
      cy.get('[data-testid="patient-last-name"]').type('Kumar')
      cy.get('[data-testid="patient-age"]').type('45')
      cy.get('[data-testid="patient-gender"]').select('Male')
      cy.get('[data-testid="patient-cancer-type"]').type('Lung Cancer')
      cy.get('[data-testid="patient-stage"]').select('Stage III')

      // Submit form
      cy.get('[data-testid="submit-patient-form"]').click()

      // Verify success
      cy.get('[data-testid="success-message"]').should('contain', 'Patient registered successfully')
      cy.get('[data-testid="patient-list"]').should('contain', 'Rajesh Kumar')
    })

    it('should validate required fields', () => {
      cy.get('[data-testid="nav-patients"]').click()
      cy.get('[data-testid="add-patient-button"]').click()

      // Try to submit empty form
      cy.get('[data-testid="submit-patient-form"]').click()

      // Check validation errors
      cy.get('[data-testid="first-name-error"]').should('contain', 'First name is required')
      cy.get('[data-testid="last-name-error"]').should('contain', 'Last name is required')
      cy.get('[data-testid="age-error"]').should('contain', 'Age is required')
    })

    it('should handle Indian name validation correctly', () => {
      cy.get('[data-testid="nav-patients"]').click()
      cy.get('[data-testid="add-patient-button"]').click()

      // Test Indian names with special characters
      cy.get('[data-testid="patient-first-name"]').type('Priya')
      cy.get('[data-testid="patient-last-name"]').type('Sharma-Patel')

      // Should accept valid Indian names
      cy.get('[data-testid="patient-first-name"]').should('have.value', 'Priya')
      cy.get('[data-testid="patient-last-name"]').should('have.value', 'Sharma-Patel')
    })
  })

  describe('Appointment Scheduling Workflow', () => {
    it('should schedule a new appointment', () => {
      // Navigate to schedule page
      cy.get('[data-testid="nav-schedule"]').click()
      cy.url().should('include', '/schedule')

      // Open new appointment form
      cy.get('[data-testid="new-appointment-button"]').click()
      cy.get('[data-testid="appointment-form-modal"]').should('be.visible')

      // Fill appointment details
      cy.get('[data-testid="appointment-patient"]').select('Priya Sharma')
      cy.get('[data-testid="appointment-date"]').type('2025-06-25')
      cy.get('[data-testid="appointment-time"]').type('10:00')
      cy.get('[data-testid="appointment-type"]').select('Consultation')
      cy.get('[data-testid="appointment-notes"]').type('Follow-up consultation for treatment progress')

      // Submit appointment
      cy.get('[data-testid="submit-appointment"]').click()

      // Verify appointment appears in schedule
      cy.get('[data-testid="appointment-list"]').should('contain', 'Priya Sharma')
      cy.get('[data-testid="appointment-list"]').should('contain', '10:00 AM')
    })

    it('should prevent scheduling conflicts', () => {
      // Try to schedule overlapping appointments
      cy.get('[data-testid="nav-schedule"]').click()
      cy.get('[data-testid="new-appointment-button"]').click()

      cy.get('[data-testid="appointment-patient"]').select('Arjun Patel')
      cy.get('[data-testid="appointment-date"]').type('2025-06-25')
      cy.get('[data-testid="appointment-time"]').type('10:00') // Same time as previous test

      cy.get('[data-testid="submit-appointment"]').click()

      // Should show conflict warning
      cy.get('[data-testid="conflict-warning"]').should('contain', 'Time slot already booked')
    })

    it('should support telemedicine appointments', () => {
      cy.get('[data-testid="nav-schedule"]').click()
      cy.get('[data-testid="new-appointment-button"]').click()

      cy.get('[data-testid="appointment-type"]').select('Telemedicine')
      
      // Should show virtual meeting options
      cy.get('[data-testid="virtual-meeting-options"]').should('be.visible')
      cy.get('[data-testid="meeting-platform"]').should('contain', 'Video Call')
    })
  })

  describe('Patient Consultation Workflow', () => {
    it('should start a video consultation', () => {
      cy.get('[data-testid="nav-consultation"]').click()
      cy.url().should('include', '/consultation')

      // Select patient for consultation
      cy.get('[data-testid="patient-consultation-list"]').first().click()

      // Start video call
      cy.get('[data-testid="start-video-call"]').click()

      // Mock video call interface
      cy.get('[data-testid="video-call-interface"]').should('be.visible')
      cy.get('[data-testid="patient-video"]').should('be.visible')
      cy.get('[data-testid="doctor-video"]').should('be.visible')
    })

    it('should send and receive messages', () => {
      cy.get('[data-testid="nav-consultation"]').click()
      cy.get('[data-testid="patient-consultation-list"]').first().click()

      // Send message
      cy.get('[data-testid="message-input"]').type('How are you feeling today?')
      cy.get('[data-testid="send-message"]').click()

      // Verify message appears
      cy.get('[data-testid="message-list"]').should('contain', 'How are you feeling today?')
      cy.get('[data-testid="message-list"] .doctor-message').should('exist')
    })

    it('should handle file attachments', () => {
      cy.get('[data-testid="nav-consultation"]').click()
      cy.get('[data-testid="patient-consultation-list"]').first().click()

      // Upload file
      cy.get('[data-testid="file-upload"]').selectFile('cypress/fixtures/test-report.pdf')
      
      // Verify file attachment
      cy.get('[data-testid="message-list"]').should('contain', 'test-report.pdf')
    })
  })

  describe('Analytics Dashboard Workflow', () => {
    it('should display treatment analytics', () => {
      cy.get('[data-testid="nav-analytics"]').click()
      cy.url().should('include', '/analytics')

      // Check KPI cards
      cy.get('[data-testid="total-patients-kpi"]').should('be.visible')
      cy.get('[data-testid="success-rate-kpi"]').should('be.visible')
      cy.get('[data-testid="ai-predictions-kpi"]').should('be.visible')

      // Check charts
      cy.get('[data-testid="treatment-outcomes-chart"]').should('be.visible')
      cy.get('[data-testid="patient-demographics-chart"]').should('be.visible')
    })

    it('should filter analytics by time period', () => {
      cy.get('[data-testid="nav-analytics"]').click()

      // Change time period
      cy.get('[data-testid="time-period-selector"]').select('Last 30 Days')

      // Verify data updates
      cy.get('[data-testid="total-patients-kpi"]').should('not.contain', 'Loading...')
      cy.get('[data-testid="treatment-outcomes-chart"]').should('be.visible')
    })

    it('should export analytics reports', () => {
      cy.get('[data-testid="nav-analytics"]').click()

      // Export report
      cy.get('[data-testid="export-report"]').click()
      cy.get('[data-testid="export-format"]').select('PDF')
      cy.get('[data-testid="confirm-export"]').click()

      // Verify download
      cy.get('[data-testid="download-link"]').should('be.visible')
    })
  })

  describe('Accessibility Compliance', () => {
    it('should support keyboard navigation', () => {
      cy.get('body').tab()
      cy.focused().should('have.attr', 'data-testid', 'skip-to-main')

      // Navigate through main menu
      cy.focused().tab()
      cy.focused().should('contain', 'Dashboard')

      cy.focused().tab()
      cy.focused().should('contain', 'Patients')
    })

    it('should have proper ARIA labels', () => {
      cy.get('[data-testid="nav-patients"]').should('have.attr', 'aria-label')
      cy.get('[data-testid="patient-search"]').should('have.attr', 'aria-label', 'Search patients')
      cy.get('[data-testid="main-content"]').should('have.attr', 'role', 'main')
    })

    it('should support screen readers', () => {
      // Check for screen reader announcements
      cy.get('[aria-live="polite"]').should('exist')
      cy.get('[aria-live="assertive"]').should('exist')

      // Test critical alert announcements
      cy.mockCriticalAlert()
      cy.get('[aria-live="assertive"]').should('contain', 'Critical alert')
    })

    it('should have sufficient color contrast', () => {
      // Test high contrast mode
      cy.enableHighContrast()
      
      // Verify contrast ratios meet medical standards
      cy.checkColorContrast('[data-testid="patient-name"]', { ratio: 7 })
      cy.checkColorContrast('[data-testid="critical-alert"]', { ratio: 7 })
    })
  })

  describe('Performance Requirements', () => {
    it('should load pages within performance budgets', () => {
      cy.visit('/', {
        onBeforeLoad: (win) => {
          win.performance.mark('start')
        },
        onLoad: (win) => {
          win.performance.mark('end')
          win.performance.measure('pageLoad', 'start', 'end')
        }
      })

      cy.window().then((win) => {
        const measure = win.performance.getEntriesByName('pageLoad')[0]
        expect(measure.duration).to.be.lessThan(3000) // 3 seconds max
      })
    })

    it('should handle 3D model loading gracefully', () => {
      cy.get('[data-testid="3d-model-viewer"]').should('be.visible')
      
      // Should show loading state
      cy.get('[data-testid="3d-model-loading"]').should('be.visible')
      
      // Should load within reasonable time
      cy.get('[data-testid="3d-model-loaded"]', { timeout: 10000 }).should('be.visible')
    })
  })

  describe('Security Compliance', () => {
    it('should require authentication for protected routes', () => {
      cy.clearAuth()
      cy.visit('/patients')
      
      // Should redirect to login
      cy.url().should('include', '/auth/signin')
    })

    it('should validate user permissions', () => {
      cy.mockAuth('nurse') // Limited permissions
      cy.visit('/patients')

      // Should not show admin functions
      cy.get('[data-testid="delete-patient"]').should('not.exist')
      cy.get('[data-testid="admin-settings"]').should('not.exist')
    })

    it('should sanitize user inputs', () => {
      cy.get('[data-testid="nav-patients"]').click()
      cy.get('[data-testid="add-patient-button"]').click()

      // Try XSS attack
      cy.get('[data-testid="patient-first-name"]').type('<script>alert("xss")</script>')
      cy.get('[data-testid="submit-patient-form"]').click()

      // Should be sanitized
      cy.get('[data-testid="patient-list"]').should('not.contain', '<script>')
    })
  })
})
