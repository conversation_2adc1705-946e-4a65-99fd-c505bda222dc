# Quant-NEX Development Setup Guide

## Project Overview

Quant-NEX is a Next.js-based medical imaging and AI analysis platform built with modern web technologies.

## Technology Stack

- **Framework**: Next.js 15.2.4 with App Router
- **Language**: TypeScript 5
- **Styling**: Tailwind CSS with custom theme
- **UI Components**: Radix UI with shadcn/ui
- **Backend**: Firebase (Firestore, Auth, Storage)
- **Package Manager**: pnpm
- **Additional Libraries**:
  - Data visualization: Recharts, Nivo
  - 3D graphics: Three.js, React Three Fiber
  - Form handling: React Hook Form with Zod validation
  - Animation: Framer Motion

## Prerequisites

- Node.js (version 18 or higher)
- pnpm (installed globally)
- Git

## Setup Instructions

### 1. Install Dependencies

```bash
# Install pnpm globally if not already installed
npm install -g pnpm

# Install project dependencies
pnpm install
```

### 2. Environment Configuration

1. Copy the environment template:
   ```bash
   cp .env.example .env.local
   ```

2. Update `.env.local` with your Firebase configuration:
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or select existing one
   - Go to Project Settings > General > Your apps
   - Copy the configuration values to `.env.local`

### 3. Firebase Setup (Optional for Demo)

The application includes demo/fallback values for Firebase, so it will work without real Firebase configuration. However, for full functionality:

1. Create a Firebase project
2. Enable Authentication (Email/Password)
3. Create Firestore database
4. Enable Storage
5. Update environment variables with real values

### 4. Development Server

```bash
# Start development server
pnpm run dev
```

The application will be available at http://localhost:3000

### 5. Build for Production

```bash
# Create production build
pnpm run build

# Start production server
pnpm run start
```

## Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Create production build
- `pnpm run start` - Start production server
- `pnpm run lint` - Run ESLint

## Known Issues and Warnings

### Peer Dependencies
- Some packages show peer dependency warnings with React 19
- These are non-critical and don't affect functionality
- Packages: react-beautiful-dnd, react-day-picker, vaul

### ESLint Warnings
- Some unused variables in dashboard components
- TypeScript `any` types in drag-drop components
- These don't prevent the application from running

## Project Structure

```
quant-nex/
├── app/                    # Next.js App Router pages
├── components/             # React components
│   ├── ui/                # shadcn/ui components
│   ├── dashboard/         # Dashboard-specific components
│   ├── auth/              # Authentication components
│   └── ...
├── lib/                   # Utility functions and Firebase config
├── contexts/              # React contexts
├── hooks/                 # Custom React hooks
├── public/                # Static assets
└── styles/                # Global styles
```

## Troubleshooting

### Port Already in Use
If port 3000 is busy, Next.js will automatically use the next available port.

### Build Errors
- Ensure all dependencies are installed: `pnpm install`
- Clear Next.js cache: `rm -rf .next`
- Restart development server

### Firebase Connection Issues
- Check environment variables in `.env.local`
- Verify Firebase project configuration
- The app works in demo mode without real Firebase

## Additional Configuration

### ESLint
ESLint is configured with Next.js strict rules. Configuration is in `.eslintrc.json`.

### Tailwind CSS
Custom theme configuration is in `tailwind.config.ts` with Quant-NEX specific colors.

### TypeScript
TypeScript configuration is in `tsconfig.json` with Next.js optimizations.

## Support

For issues or questions:
1. Check this documentation
2. Review the console for error messages
3. Ensure all dependencies are properly installed
4. Verify environment configuration
