{"name": "Quant-NEX Oncology Platform", "short_name": "Quant-NEX", "description": "Advanced oncology platform with AI-powered insights and 3D medical visualization for healthcare professionals in India", "start_url": "/", "display": "standalone", "background_color": "#000000", "theme_color": "#3b82f6", "orientation": "portrait-primary", "scope": "/", "lang": "en-IN", "categories": ["medical", "health", "productivity"], "screenshots": [{"src": "/screenshots/desktop-dashboard.png", "sizes": "1280x720", "type": "image/png", "platform": "wide", "label": "Dashboard view showing patient analytics and 3D medical models"}, {"src": "/screenshots/mobile-patients.png", "sizes": "390x844", "type": "image/png", "platform": "narrow", "label": "Patient management interface optimized for mobile devices"}], "icons": [{"src": "/icons/icon-72x72.png", "sizes": "72x72", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-96x96.png", "sizes": "96x96", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-128x128.png", "sizes": "128x128", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-144x144.png", "sizes": "144x144", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-152x152.png", "sizes": "152x152", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-192x192.png", "sizes": "192x192", "type": "image/png", "purpose": "any maskable"}, {"src": "/icons/icon-384x384.png", "sizes": "384x384", "type": "image/png", "purpose": "any"}, {"src": "/icons/icon-512x512.png", "sizes": "512x512", "type": "image/png", "purpose": "any maskable"}], "shortcuts": [{"name": "Patient Dashboard", "short_name": "Patients", "description": "View and manage patient information", "url": "/patients", "icons": [{"src": "/icons/shortcut-patients.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Schedule", "short_name": "Schedule", "description": "Manage appointments and consultations", "url": "/schedule", "icons": [{"src": "/icons/shortcut-schedule.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Emergency Consultation", "short_name": "Emergency", "description": "Start emergency video consultation", "url": "/consultation?emergency=true", "icons": [{"src": "/icons/shortcut-emergency.png", "sizes": "96x96", "type": "image/png"}]}, {"name": "Analytics", "short_name": "Analytics", "description": "View treatment analytics and insights", "url": "/analytics", "icons": [{"src": "/icons/shortcut-analytics.png", "sizes": "96x96", "type": "image/png"}]}], "related_applications": [], "prefer_related_applications": false, "edge_side_panel": {"preferred_width": 400}, "launch_handler": {"client_mode": "focus-existing"}, "handle_links": "preferred", "protocol_handlers": [{"protocol": "web+quantnex", "url": "/protocol-handler?url=%s"}], "file_handlers": [{"action": "/file-handler", "accept": {"application/dicom": [".dcm", ".dicom"], "application/pdf": [".pdf"], "image/jpeg": [".jpg", ".jpeg"], "image/png": [".png"]}, "icons": [{"src": "/icons/file-handler.png", "sizes": "96x96", "type": "image/png"}], "launch_type": "single-client"}], "share_target": {"action": "/share-target", "method": "POST", "enctype": "multipart/form-data", "params": {"title": "title", "text": "text", "url": "url", "files": [{"name": "medical_files", "accept": ["application/dicom", "application/pdf", "image/*"]}]}}}