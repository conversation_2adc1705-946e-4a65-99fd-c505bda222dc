{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"build": "next build", "dev": "next dev", "lint": "next lint", "start": "next start", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest --testPathPattern=__tests__/unit", "test:integration": "jest --testPathPattern=__tests__/integration", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:a11y": "pa11y-ci --sitemap http://localhost:3000/sitemap.xml", "test:hipaa": "jest --testPathPattern=__tests__/compliance", "test:medical-devices": "jest --testPathPattern=__tests__/medical-devices", "audit:privacy": "node scripts/privacy-audit.js", "audit:security": "npm audit && node scripts/security-audit.js", "generate:compliance-report": "node scripts/generate-compliance-report.js", "build:analyze": "ANALYZE=true next build", "build:production": "NODE_ENV=production next build", "build:staging": "NODE_ENV=staging next build", "lighthouse": "lhci autorun", "performance:audit": "node scripts/performance-audit.js"}, "dependencies": {"@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@nivo/line": "latest", "@nivo/radar": "latest", "@radix-ui/react-accordion": "1.2.2", "@radix-ui/react-alert-dialog": "1.1.4", "@radix-ui/react-aspect-ratio": "1.1.1", "@radix-ui/react-avatar": "1.1.2", "@radix-ui/react-checkbox": "1.1.3", "@radix-ui/react-collapsible": "1.1.2", "@radix-ui/react-context-menu": "2.2.4", "@radix-ui/react-dialog": "1.1.4", "@radix-ui/react-dropdown-menu": "2.1.4", "@radix-ui/react-hover-card": "1.1.4", "@radix-ui/react-label": "2.1.1", "@radix-ui/react-menubar": "1.1.4", "@radix-ui/react-navigation-menu": "1.2.3", "@radix-ui/react-popover": "1.1.4", "@radix-ui/react-progress": "1.1.1", "@radix-ui/react-radio-group": "1.2.2", "@radix-ui/react-scroll-area": "1.2.2", "@radix-ui/react-select": "2.1.4", "@radix-ui/react-separator": "1.1.1", "@radix-ui/react-slider": "1.2.2", "@radix-ui/react-slot": "1.1.1", "@radix-ui/react-switch": "1.1.2", "@radix-ui/react-tabs": "1.1.2", "@radix-ui/react-toast": "1.2.4", "@radix-ui/react-toggle": "1.1.1", "@radix-ui/react-toggle-group": "1.1.1", "@radix-ui/react-tooltip": "1.1.6", "@react-three/drei": "latest", "@react-three/fiber": "latest", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "critters": "^0.0.25", "cssesc": "^3.0.0", "date-fns": "4.1.0", "embla-carousel-react": "8.5.1", "expo": "latest", "expo-asset": "latest", "expo-file-system": "latest", "expo-gl": "latest", "firebase": "latest", "framer-motion": "latest", "input-otp": "1.4.1", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "react": "^19", "react-beautiful-dnd": "latest", "react-day-picker": "8.10.1", "react-dom": "^19", "react-hook-form": "^7.54.1", "react-intersection-observer": "latest", "react-native": "latest", "react-resizable-panels": "^2.1.7", "recharts": "latest", "sonner": "^1.7.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "three": "latest", "vaul": "^0.9.6", "zod": "^3.24.1"}, "devDependencies": {"@types/node": "^22", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9.29.0", "eslint-config-next": "15.3.4", "postcss": "^8.5", "tailwindcss": "^3.4.17", "typescript": "^5"}}