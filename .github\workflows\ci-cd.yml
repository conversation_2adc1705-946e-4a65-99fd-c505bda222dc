name: Quant-NEX CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:

env:
  NODE_VERSION: '18'
  PNPM_VERSION: '8'

jobs:
  # Code Quality and Security Checks
  quality-checks:
    name: Code Quality & Security
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: TypeScript type checking
      run: npm run type-check

    - name: ESLint
      run: npm run lint

    - name: Prettier format check
      run: npm run format:check

    - name: Security audit
      run: npm audit --audit-level=moderate

    - name: Dependency vulnerability scan
      uses: actions/dependency-review-action@v3
      if: github.event_name == 'pull_request'

  # Unit and Integration Tests
  test:
    name: Unit & Integration Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    
    strategy:
      matrix:
        node-version: [18, 20]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run unit tests
      run: npm run test:unit -- --coverage --watchAll=false

    - name: Run integration tests
      run: npm run test:integration -- --watchAll=false

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        file: ./coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

    - name: Upload test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: test-results-${{ matrix.node-version }}
        path: |
          test-results/
          coverage/

  # Accessibility Testing
  accessibility:
    name: Accessibility Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production

    - name: Wait for application
      run: npx wait-on http://localhost:3000

    - name: Run accessibility tests
      run: npm run test:a11y

    - name: Upload accessibility report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: accessibility-report
        path: accessibility-report/

  # End-to-End Tests
  e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    needs: [test, accessibility]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright browsers
      run: npx playwright install --with-deps

    - name: Build application
      run: npm run build

    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production

    - name: Wait for application
      run: npx wait-on http://localhost:3000

    - name: Run E2E tests
      run: npm run test:e2e

    - name: Upload E2E test results
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: e2e-results
        path: |
          test-results/
          playwright-report/

  # Performance Testing
  performance:
    name: Performance Tests
    runs-on: ubuntu-latest
    needs: quality-checks
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build

    - name: Start application
      run: npm start &
      env:
        NODE_ENV: production

    - name: Wait for application
      run: npx wait-on http://localhost:3000

    - name: Run Lighthouse CI
      run: |
        npm install -g @lhci/cli@0.12.x
        lhci autorun
      env:
        LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

    - name: Upload Lighthouse report
      uses: actions/upload-artifact@v3
      if: always()
      with:
        name: lighthouse-report
        path: .lighthouseci/

  # Security Scanning
  security:
    name: Security Scan
    runs-on: ubuntu-latest
    needs: quality-checks
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: Upload Trivy scan results
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: CodeQL Analysis
      uses: github/codeql-action/init@v2
      with:
        languages: javascript

    - name: Autobuild
      uses: github/codeql-action/autobuild@v2

    - name: Perform CodeQL Analysis
      uses: github/codeql-action/analyze@v2

  # Build and Deploy
  build-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [test, accessibility, e2e, performance, security]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Build application
      run: npm run build
      env:
        NODE_ENV: production

    - name: Generate deployment package
      run: |
        zip -r deployment.zip .next/ public/ package.json package-lock.json
        
    - name: Upload deployment artifact
      uses: actions/upload-artifact@v3
      with:
        name: deployment-package
        path: deployment.zip

    # Add your deployment steps here
    # Example for Vercel:
    # - name: Deploy to Vercel
    #   uses: amondnet/vercel-action@v25
    #   with:
    #     vercel-token: ${{ secrets.VERCEL_TOKEN }}
    #     vercel-org-id: ${{ secrets.ORG_ID }}
    #     vercel-project-id: ${{ secrets.PROJECT_ID }}
    #     vercel-args: '--prod'

  # Medical Compliance Checks
  compliance:
    name: Medical Compliance
    runs-on: ubuntu-latest
    needs: [test, security]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: HIPAA Compliance Check
      run: npm run test:hipaa

    - name: Data Privacy Audit
      run: npm run audit:privacy

    - name: Medical Device Integration Test
      run: npm run test:medical-devices

    - name: Generate compliance report
      run: npm run generate:compliance-report

    - name: Upload compliance report
      uses: actions/upload-artifact@v3
      with:
        name: compliance-report
        path: compliance-report/
