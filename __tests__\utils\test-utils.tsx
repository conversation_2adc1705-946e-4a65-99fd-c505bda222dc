import React, { ReactElement } from 'react'
import { render, RenderOptions } from '@testing-library/react'
import { SessionProvider } from 'next-auth/react'
import { Session } from 'next-auth'

// Mock medical data for testing
export const mockPatientData = {
  id: 'P-1001',
  firstName: 'Priya',
  lastName: '<PERSON>',
  age: 42,
  gender: 'Female' as const,
  cancerType: 'Glioblastoma',
  stage: 'IV' as const,
  status: 'active' as const,
  treatmentProgress: 65,
  lastVisit: '2025-05-10',
  medicalHistory: 'Patient diagnosed with glioblastoma in 2024. Previous treatments include surgery and chemotherapy.',
  allergies: ['Penicillin', 'Latex'],
  medications: ['Temozolomide', 'Dexamethasone'],
}

export const mockDoctorSession: Session = {
  user: {
    id: '1',
    email: '<EMAIL>',
    name: 'Dr. <PERSON><PERSON>',
    role: 'doctor',
    specialization: 'Oncology',
    licenseNumber: 'MCI-12345',
    department: 'Oncology',
    permissions: ['read_patients', 'write_patients', 'read_reports', 'write_reports'],
  },
  expires: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(), // 8 hours
}

export const mockNurseSession: Session = {
  user: {
    id: '2',
    email: '<EMAIL>',
    name: 'Nurse Priya Patel',
    role: 'nurse',
    specialization: 'Oncology Nursing',
    licenseNumber: 'RN-67890',
    department: 'Oncology',
    permissions: ['read_patients', 'read_reports'],
  },
  expires: new Date(Date.now() + 8 * 60 * 60 * 1000).toISOString(),
}

export const mockAppointmentData = {
  id: 'A-1001',
  patientId: 'P-1001',
  doctorId: '1',
  date: '2025-06-21T10:00:00Z',
  type: 'consultation' as const,
  status: 'scheduled' as const,
  notes: 'Follow-up consultation for treatment progress review',
  location: 'Room 101',
}

export const mockReportData = {
  id: 'R-1001',
  patientId: 'P-1001',
  type: 'lab' as const,
  title: 'Blood Test Results',
  content: 'Complete blood count shows improvement in white cell count.',
  authorId: '1',
  date: '2025-06-20T14:30:00Z',
  attachments: ['blood-test-results.pdf'],
}

export const mockAnalyticsData = {
  totalPatients: 1247,
  successRate: 94.2,
  aiPredictions: 156,
  activeTreatments: 89,
  treatmentOutcomes: [
    { treatment: 'Chemotherapy', success: 87, total: 120, rate: 72.5 },
    { treatment: 'Radiation', success: 94, total: 110, rate: 85.5 },
    { treatment: 'Immunotherapy', success: 45, total: 52, rate: 86.5 },
    { treatment: 'Surgery', success: 78, total: 85, rate: 91.8 },
  ],
}

// Custom render function with providers
interface CustomRenderOptions extends Omit<RenderOptions, 'wrapper'> {
  session?: Session | null
  initialProps?: Record<string, any>
}

function AllTheProviders({ 
  children, 
  session = mockDoctorSession 
}: { 
  children: React.ReactNode
  session?: Session | null 
}) {
  return (
    <SessionProvider session={session}>
      {children}
    </SessionProvider>
  )
}

export function renderWithProviders(
  ui: ReactElement,
  options: CustomRenderOptions = {}
) {
  const { session, ...renderOptions } = options

  return render(ui, {
    wrapper: ({ children }) => (
      <AllTheProviders session={session}>
        {children}
      </AllTheProviders>
    ),
    ...renderOptions,
  })
}

// Medical-specific test utilities
export const medicalTestUtils = {
  // Simulate patient data loading
  mockPatientDataLoading: (delay = 100) => {
    return new Promise((resolve) => {
      setTimeout(() => resolve(mockPatientData), delay)
    })
  },

  // Simulate medical device data
  mockMedicalDeviceData: () => ({
    heartRate: 72,
    bloodPressure: { systolic: 120, diastolic: 80 },
    temperature: 36.5,
    oxygenSaturation: 98,
    timestamp: new Date().toISOString(),
  }),

  // Simulate 3D model loading
  mock3DModelLoading: (quality: 'low' | 'medium' | 'high' = 'medium') => {
    const loadTimes = { low: 500, medium: 1500, high: 3000 }
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({
          url: `/models/brain_${quality}.glb`,
          loaded: true,
          quality,
        })
      }, loadTimes[quality])
    })
  },

  // Simulate API responses
  mockApiResponse: function<T>(data: T, delay = 100, shouldFail = false) {
    return new Promise<T>((resolve, reject) => {
      setTimeout(() => {
        if (shouldFail) {
          reject(new Error('API request failed'))
        } else {
          resolve(data)
        }
      }, delay)
    })
  },

  // Generate test patient data
  generateTestPatient: (overrides: Partial<typeof mockPatientData> = {}) => {
    return {
      ...mockPatientData,
      ...overrides,
      id: `P-${Math.random().toString(36).substr(2, 9)}`,
    }
  },

  // Generate test appointment data
  generateTestAppointment: (overrides: Partial<typeof mockAppointmentData> = {}) => {
    return {
      ...mockAppointmentData,
      ...overrides,
      id: `A-${Math.random().toString(36).substr(2, 9)}`,
    }
  },
}

// Accessibility testing utilities
export const a11yTestUtils = {
  // Check for proper ARIA labels
  expectAriaLabel: (element: HTMLElement, expectedLabel: string) => {
    expect(element).toHaveAttribute('aria-label', expectedLabel)
  },

  // Check for proper heading hierarchy
  expectHeadingHierarchy: (container: HTMLElement) => {
    const headings = container.querySelectorAll('h1, h2, h3, h4, h5, h6')
    const levels = Array.from(headings).map(h => parseInt(h.tagName.charAt(1)))
    
    for (let i = 1; i < levels.length; i++) {
      expect(levels[i] - levels[i - 1]).toBeLessThanOrEqual(1)
    }
  },

  // Check for keyboard navigation
  expectKeyboardAccessible: (element: HTMLElement) => {
    expect(element).toHaveAttribute('tabindex')
    expect(element).toHaveAttribute('role')
  },

  // Check for screen reader announcements
  expectScreenReaderAnnouncement: (container: HTMLElement, text: string) => {
    const srOnly = container.querySelector('[aria-live]')
    expect(srOnly).toHaveTextContent(text)
  },
}

// Performance testing utilities
export const performanceTestUtils = {
  // Measure component render time
  measureRenderTime: async (renderFn: () => void) => {
    const start = performance.now()
    renderFn()
    const end = performance.now()
    return end - start
  },

  // Simulate slow network for 3D models
  simulateSlowNetwork: () => {
    // Mock slow network conditions
    Object.defineProperty(navigator, 'connection', {
      value: {
        effectiveType: '2g',
        downlink: 0.5,
      },
      writable: true,
    })
  },

  // Simulate fast network
  simulateFastNetwork: () => {
    Object.defineProperty(navigator, 'connection', {
      value: {
        effectiveType: '4g',
        downlink: 10,
      },
      writable: true,
    })
  },
}

// Security testing utilities
export const securityTestUtils = {
  // Test input sanitization
  expectSanitizedInput: (input: string, expected: string) => {
    // This would integrate with your actual sanitization functions
    expect(input).toBe(expected)
  },

  // Test CSRF protection
  expectCSRFProtection: (request: any) => {
    expect(request.headers).toHaveProperty('x-csrf-token')
  },

  // Test authentication
  expectAuthenticated: (session: Session | null) => {
    expect(session).not.toBeNull()
    expect(session?.user).toBeDefined()
  },

  // Test authorization
  expectAuthorized: (session: Session, requiredPermission: string) => {
    expect(session.user.permissions).toContain(requiredPermission)
  },
}

// Re-export everything from testing-library
export * from '@testing-library/react'
export { default as userEvent } from '@testing-library/user-event'

// Export custom render as default
export { renderWithProviders as render }
