// Environment setup for Jest tests

// Set test environment variables
process.env.NODE_ENV = 'test'
process.env.NEXT_PUBLIC_APP_ENV = 'test'

// Mock environment variables for testing
process.env.NEXTAUTH_SECRET = 'test-secret-key-for-jwt-testing-only'
process.env.NEXTAUTH_URL = 'http://localhost:3000'
process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/quantnex_test'
process.env.REDIS_URL = 'redis://localhost:6379/1'

// Medical API mocks
process.env.MEDICAL_API_KEY = 'test-medical-api-key'
process.env.DICOM_SERVER_URL = 'http://localhost:8080/dicom'
process.env.DICOM_API_KEY = 'test-dicom-api-key'

// Security test keys
process.env.ENCRYPTION_KEY = 'test-encryption-key-32-characters'
process.env.AUDIT_LOG_ENDPOINT = 'http://localhost:3001/audit'

// Disable external services in tests
process.env.DISABLE_EXTERNAL_SERVICES = 'true'
process.env.MOCK_MEDICAL_DEVICES = 'true'

// Test-specific configurations
process.env.HIPAA_COMPLIANCE_MODE = 'false' // Disable for testing
process.env.RATE_LIMIT_DISABLED = 'true'
process.env.CACHE_DISABLED = 'true'
